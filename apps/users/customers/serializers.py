"""
customers serializers
"""
from django.core.files.storage import default_storage
from django.db.models import Sum
from rest_framework import serializers

from apps.users.common.utils import create_username
from apps.users.models import Customer, PhoneToken, User, UserTypes
from apps.users.vendors.models import VendorClient


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model.
    """

    class Meta:
        """
        the meta class
        """
        model = User
        fields = ("id", "phone_number", "user_type")
        read_only_fields = ("user_type",)

    def create(self, validated_data):
        """
        Create and return a new user using the custom create_user method.
        """
        return User.objects.create_user(**validated_data)


class CustomerSerializer(serializers.ModelSerializer):
    """
    Serializer for retrieving Customer data.
    Includes nested User data and return bottle count from VendorClient.
    """
    user = UserSerializer()
    return_bottles = serializers.SerializerMethodField()

    class Meta:
        """
        the meta class
        """
        model = Customer
        fields = (
            "id",
            "user",
            "fullname",
            "avatar",
            "number1",
            "number2",
            "longitude",
            "latitude",
            "passport",
            "region",
            "district",
            "user_type",
            "return_bottles",
            "address",
        )
        read_only_fields = ("user_type",)

    def get_return_bottles(self, obj):
        """
        Return total return_bottles aggregated from VendorClient linked to the customer.
        """
        total = VendorClient.objects.filter(customer=obj).aggregate(
            total=Sum("return_bottles")
        )["total"] or 0
        return total


class CustomerCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating Customer instances.
    Includes nested user creation and avatar file handling.
    """
    user = UserSerializer()

    class Meta:
        """
        the meta class
        """
        model = Customer
        fields = (
            "id",
            "user",
            "fullname",
            "avatar",
            "number1",
            "number2",
            "longitude",
            "latitude",
            "passport",
            "region",
            "district",
            "user_type",
            "address",
        )
        read_only_fields = ("user_type", "number1")

    def create(self, validated_data):
        """
        Create a new Customer along with a nested User.
        Assigns default username and sets user_type to CUSTOMER.
        """
        user_data = validated_data.pop("user")
        phone_number = user_data.get("phone_number")

        # Check if customer user with this phone number already exists
        if phone_number:
            existing_user = User.objects.filter(
                phone_number=phone_number,
                user_type=UserTypes.CUSTOMER
            ).first()
            if existing_user:
                raise serializers.ValidationError(
                    "Phone number already registered as Customer"
                )

        username = create_username()
        user_data["username"] = username

        # update or create user by phone number
        user, _ = User.objects.update_or_create(
            phone_number=phone_number,
            defaults=user_data
        )
        customer, _ = Customer.objects.update_or_create(user=user, **validated_data)
        return customer

    def update(self, instance, validated_data):
        """
        Update an existing Customer.
        Restricts access to the customer themselves if the request user is of type CUSTOMER.
        Handles avatar file upload via default storage (e.g., S3).
        """
        request_user = self.context.get("request").user

        if request_user.user_type == UserTypes.CUSTOMER:
            if request_user.customer != instance:
                raise serializers.ValidationError(
                    {"detail": "You don't have permission to perform this action."}
                )

        avatar = validated_data.pop("avatar", None)
        if avatar:
            try:
                file_name = default_storage.save(f"customers/{avatar.name}", avatar)
                file_url = default_storage.url(file_name)
                validated_data["avatar"] = file_url
            except Exception as e:
                raise serializers.ValidationError(
                    {"avatar": f"File upload failed: {str(e)}"}
                )

        return super().update(instance, validated_data)


class PhoneTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for reading PhoneToken model, typically used for verification.
    """
    class Meta:
        """
        the meta class
        """
        model = PhoneToken
        fields = ["phone_number", "token"]


class PhoneTokenResendSerializer(serializers.ModelSerializer):
    """
    Serializer for resending phone tokens based on phone number.
    """
    class Meta:
        """
        the meta class
        """
        model = PhoneToken
        fields = ["phone_number"]